{"rustc": 10895048813736897673, "features": "[\"alloc\"]", "declared_features": "[\"alloc\", \"default\", \"fresh-rust\", \"nightly\", \"serde\", \"std\"]", "target": 5388200169723499962, "profile": 13786820282418960226, "path": 17136026426704097334, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\allocator-api2-5c57794471001d7d\\dep-lib-allocator_api2", "checksum": false}}], "rustflags": ["-C", "target-cpu=native", "-C", "opt-level=3", "-C", "lto=fat", "-C", "codegen-units=1", "-C", "panic=abort"], "config": 2069994364910194474, "compile_kind": 0}