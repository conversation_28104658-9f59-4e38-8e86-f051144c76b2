{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[\"core\", \"rustc-dep-of-std\"]", "target": 13840298032947503755, "profile": 3592778941406178886, "path": 16745500087690977778, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\cfg-if-4bdc90105993f735\\dep-lib-cfg_if", "checksum": false}}], "rustflags": ["-C", "target-cpu=native", "-C", "opt-level=3", "-C", "lto=fat", "-C", "codegen-units=1", "-C", "panic=abort"], "config": 2069994364910194474, "compile_kind": 0}