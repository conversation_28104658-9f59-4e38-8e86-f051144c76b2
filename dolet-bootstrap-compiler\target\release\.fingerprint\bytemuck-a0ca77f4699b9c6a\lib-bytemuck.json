{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[\"aarch64_simd\", \"align_offset\", \"alloc_uninit\", \"avx512_simd\", \"bytemuck_derive\", \"const_zeroed\", \"derive\", \"extern_crate_alloc\", \"extern_crate_std\", \"impl_core_error\", \"latest_stable_rust\", \"min_const_generics\", \"must_cast\", \"must_cast_extra\", \"nightly_docs\", \"nightly_float\", \"nightly_portable_simd\", \"nightly_stdsimd\", \"pod_saturating\", \"track_caller\", \"transparentwrapper_extra\", \"unsound_ptr_pod_impl\", \"wasm_simd\", \"zeroable_atomics\", \"zeroable_maybe_uninit\", \"zeroable_unwind_fn\"]", "target": 5195934831136530909, "profile": 10986745973234452799, "path": 3110081939357954033, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\bytemuck-a0ca77f4699b9c6a\\dep-lib-bytemuck", "checksum": false}}], "rustflags": ["-C", "target-cpu=native", "-C", "opt-level=3", "-C", "lto=fat", "-C", "codegen-units=1", "-C", "panic=abort"], "config": 2069994364910194474, "compile_kind": 0}