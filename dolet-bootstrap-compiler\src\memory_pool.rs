/// Ultra-fast Memory Pool for Zero-Allocation Compilation
/// Pre-allocates memory pools to eliminate allocation overhead during compilation

use std::ptr::NonNull;
use std::alloc::{alloc, dealloc, Layout};

pub struct MemoryPool {
    pools: Vec<Pool>,
    current_pool: usize,
}

struct Pool {
    memory: NonNull<u8>,
    size: usize,
    used: usize,
    layout: Layout,
}

impl MemoryPool {
    pub fn new() -> Self {
        let mut pools = Vec::new();
        
        // Pre-allocate pools of different sizes for different use cases
        pools.push(Pool::new(1024));      // Small allocations (tokens)
        pools.push(Pool::new(4096));      // Medium allocations (AST nodes)
        pools.push(Pool::new(16384));     // Large allocations (symbol tables)
        pools.push(Pool::new(65536));     // Very large allocations (code generation)
        
        Self {
            pools,
            current_pool: 0,
        }
    }

    pub fn allocate<T>(&mut self, count: usize) -> Option<NonNull<T>> {
        let size = std::mem::size_of::<T>() * count;
        let align = std::mem::align_of::<T>();
        
        // Try to allocate from existing pools
        for (i, pool) in self.pools.iter_mut().enumerate() {
            if let Some(ptr) = pool.allocate(size, align) {
                self.current_pool = i;
                return Some(ptr.cast());
            }
        }
        
        // If no pool has space, create a new one
        let new_size = (size * 2).max(4096); // At least double the requested size
        let mut new_pool = Pool::new(new_size);
        
        if let Some(ptr) = new_pool.allocate(size, align) {
            self.pools.push(new_pool);
            self.current_pool = self.pools.len() - 1;
            Some(ptr.cast())
        } else {
            None
        }
    }

    pub fn reset(&mut self) {
        // Reset all pools for reuse
        for pool in &mut self.pools {
            pool.reset();
        }
        self.current_pool = 0;
    }

    pub fn total_allocated(&self) -> usize {
        self.pools.iter().map(|p| p.used).sum()
    }

    pub fn total_capacity(&self) -> usize {
        self.pools.iter().map(|p| p.size).sum()
    }
}

impl Pool {
    fn new(size: usize) -> Self {
        let layout = Layout::from_size_align(size, 8).unwrap();
        let memory = unsafe {
            let ptr = alloc(layout);
            if ptr.is_null() {
                panic!("Failed to allocate memory pool");
            }
            NonNull::new_unchecked(ptr)
        };
        
        Self {
            memory,
            size,
            used: 0,
            layout,
        }
    }

    fn allocate(&mut self, size: usize, align: usize) -> Option<NonNull<u8>> {
        // Align the current position
        let aligned_used = (self.used + align - 1) & !(align - 1);
        
        if aligned_used + size <= self.size {
            let ptr = unsafe {
                NonNull::new_unchecked(self.memory.as_ptr().add(aligned_used))
            };
            self.used = aligned_used + size;
            Some(ptr)
        } else {
            None
        }
    }

    fn reset(&mut self) {
        self.used = 0;
    }
}

impl Drop for Pool {
    fn drop(&mut self) {
        unsafe {
            dealloc(self.memory.as_ptr(), self.layout);
        }
    }
}

unsafe impl Send for MemoryPool {}
unsafe impl Sync for MemoryPool {}

/// Thread-local memory pool for ultra-fast allocations
thread_local! {
    static MEMORY_POOL: std::cell::RefCell<MemoryPool> = std::cell::RefCell::new(MemoryPool::new());
}

pub fn with_memory_pool<F, R>(f: F) -> R
where
    F: FnOnce(&mut MemoryPool) -> R,
{
    MEMORY_POOL.with(|pool| {
        let mut pool = pool.borrow_mut();
        f(&mut *pool)
    })
}

pub fn reset_memory_pool() {
    MEMORY_POOL.with(|pool| {
        pool.borrow_mut().reset();
    });
}

/// Fast allocator for compilation-time objects
pub struct FastAllocator;

impl FastAllocator {
    pub fn alloc<T>(&self, value: T) -> *mut T {
        with_memory_pool(|pool| {
            if let Some(ptr) = pool.allocate::<T>(1) {
                unsafe {
                    ptr.as_ptr().write(value);
                    ptr.as_ptr()
                }
            } else {
                // Fallback to system allocator
                Box::into_raw(Box::new(value))
            }
        })
    }

    pub fn alloc_slice<T>(&self, slice: &[T]) -> *mut [T] 
    where
        T: Clone,
    {
        with_memory_pool(|pool| {
            if let Some(ptr) = pool.allocate::<T>(slice.len()) {
                unsafe {
                    for (i, item) in slice.iter().enumerate() {
                        ptr.as_ptr().add(i).write(item.clone());
                    }
                    std::ptr::slice_from_raw_parts_mut(ptr.as_ptr(), slice.len())
                }
            } else {
                // Fallback to system allocator
                Box::into_raw(slice.to_vec().into_boxed_slice())
            }
        })
    }
}

/// Compile-time memory statistics
pub struct MemoryStats {
    pub total_allocated: usize,
    pub total_capacity: usize,
    pub utilization: f64,
    pub pools_count: usize,
}

pub fn get_memory_stats() -> MemoryStats {
    with_memory_pool(|pool| {
        let total_allocated = pool.total_allocated();
        let total_capacity = pool.total_capacity();
        let utilization = if total_capacity > 0 {
            total_allocated as f64 / total_capacity as f64
        } else {
            0.0
        };
        
        MemoryStats {
            total_allocated,
            total_capacity,
            utilization,
            pools_count: pool.pools.len(),
        }
    })
}
