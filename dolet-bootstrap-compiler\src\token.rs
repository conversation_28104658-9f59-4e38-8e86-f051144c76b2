use std::fmt;

/// High-performance token representation for Dolet language
/// Uses zero-copy string slices for maximum performance
#[derive(Debug, <PERSON>lone, PartialEq)]
pub struct Token<'a> {
    pub kind: TokenKind,
    pub lexeme: &'a str,
    pub line: usize,
    pub column: usize,
}

#[derive(Debug, Clone, PartialEq, Copy)]
#[allow(dead_code)] // Future language features
pub enum TokenKind {
    // Literals
    Integer(i64),
    Float(f32),
    Double(f64),
    String,
    Char,
    <PERSON><PERSON>an(bool),
    Null,

    // Identifiers
    Identifier,

    // Keywords
    Set,
    Const,
    Fun,
    Fn,           // Alternative for fn
    End,
    If,
    Else,
    For,
    While,
    Do,           // For while loops: while condition do
    Match,
    Case,
    Default,
    Class,
    Struct,
    Interface,
    Enum,
    Inherits,
    Implements,
    Private,
    Public,
    Static,
    Return,
    Try,
    Catch,
    Finally,
    Native,
    Call,
    Macro,
    From,
    To,
    In,
    Into,
    As,
    True,
    False,

    // Built-in functions
    Say,
    Ask,
    Input,
    Read,
    Write,
    Append,
    Wait,

    // Operators
    Plus,
    Minus,
    Star,        // *
    Slash,       // /
    Percent,     // %
    Multiply,
    Divide,
    Modulo,
    Assign,
    PlusAssign,
    MinusAssign,
    MultiplyAssign,
    DivideAssign,
    ModuloAssign,
    Equal,
    EqualEqual,   // ==
    Bang,         // !
    BangEqual,    // !=
    NotEqual,
    Less,
    LessEqual,
    Greater,
    GreaterEqual,
    And,
    Or,
    Not,
    Arrow,        // ->

    // Delimiters
    LeftParen,
    RightParen,
    LeftBracket,
    RightBracket,
    LeftBrace,
    RightBrace,
    Comma,
    Colon,
    Semicolon,
    Dot,
    Question,     // For nullable types

    // Special
    Newline,
    Eof,
    Comment,
    Whitespace,
}

/// Dolet type system for ultra-fast type inference
#[derive(Debug, Clone, PartialEq)]
#[allow(dead_code)] // Future language features
pub enum DoletType {
    Int,
    Float,
    Double,
    String,
    Char,
    Bool,
    Null,
    Unknown,
    // Nullable wrapper for any type
    Nullable(Box<DoletType>),
    // Collections
    Array(Box<DoletType>),
    Map(Box<DoletType>, Box<DoletType>),
    Set(Box<DoletType>),
    Tuple(Vec<DoletType>),
    // User-defined types
    Struct(String),
    Class(String),
    Interface(String),
    Enum(String),
    // Function type
    Function(Vec<DoletType>, Box<DoletType>),
    // Generic type
    Generic(String, Vec<DoletType>),
}

impl<'a> Token<'a> {
    pub fn new(kind: TokenKind, lexeme: &'a str, line: usize, column: usize) -> Self {
        Self {
            kind,
            lexeme,
            line,
            column,
        }
    }
}

impl fmt::Display for TokenKind {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            TokenKind::Integer(n) => write!(f, "Integer({})", n),
            TokenKind::Float(n) => write!(f, "Float({})", n),
            TokenKind::Double(n) => write!(f, "Double({})", n),
            TokenKind::Boolean(b) => write!(f, "Boolean({})", b),
            _ => write!(f, "{:?}", self),
        }
    }
}

#[allow(dead_code)] // Future language features
impl DoletType {
    /// Check if this type can be null
    pub fn is_nullable(&self) -> bool {
        matches!(self, DoletType::Nullable(_))
    }

    /// Get the underlying type if nullable, otherwise return self
    pub fn unwrap_nullable(&self) -> &DoletType {
        match self {
            DoletType::Nullable(inner) => inner,
            _ => self,
        }
    }

    /// Make this type nullable
    pub fn make_nullable(self) -> DoletType {
        if self.is_nullable() {
            self
        } else {
            DoletType::Nullable(Box::new(self))
        }
    }
}

/// Ultra-fast type inference from token
/// This is the core of Dolet's performance advantage
#[inline]
pub fn infer_type_from_token(lexeme: &str) -> DoletType {
    // Fast path: check first character for immediate classification
    let first_char = match lexeme.chars().next() {
        Some(c) => c,
        None => return DoletType::Unknown,
    };

    match first_char {
        // String literals
        '"' => DoletType::String,
        // Character literals
        '\'' => DoletType::Char,
        // Numeric literals
        '0'..='9' => infer_numeric_type(lexeme),
        // Check for boolean or null keywords
        _ => match lexeme {
            "true" | "false" => DoletType::Bool,
            "null" => DoletType::Null,
            _ => DoletType::Unknown,
        }
    }
}

/// High-performance numeric type inference with early exit optimization
#[inline]
fn infer_numeric_type(lexeme: &str) -> DoletType {
    let mut has_dot = false;
    let mut decimal_places = 0;
    
    for ch in lexeme.chars() {
        match ch {
            '0'..='9' => {
                if has_dot {
                    decimal_places += 1;
                    // Early exit optimization: if we reach 6 decimal places, it's a Double
                    if decimal_places >= 6 {
                        return DoletType::Double;
                    }
                }
            }
            '.' => {
                if has_dot {
                    // Invalid: multiple dots
                    return DoletType::Unknown;
                }
                has_dot = true;
            }
            _ => return DoletType::Unknown, // Invalid character
        }
    }

    if has_dot {
        // Float vs Double based on decimal places
        if decimal_places >= 6 {
            DoletType::Double
        } else {
            DoletType::Float
        }
    } else {
        DoletType::Int
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_type_inference() {
        assert_eq!(infer_type_from_token("42"), DoletType::Int);
        assert_eq!(infer_type_from_token("3.14"), DoletType::Float);
        assert_eq!(infer_type_from_token("3.123456789"), DoletType::Double);
        assert_eq!(infer_type_from_token("\"hello\""), DoletType::String);
        assert_eq!(infer_type_from_token("'a'"), DoletType::Char);
        assert_eq!(infer_type_from_token("true"), DoletType::Bool);
        assert_eq!(infer_type_from_token("false"), DoletType::Bool);
        assert_eq!(infer_type_from_token("null"), DoletType::Null);
    }

    #[test]
    fn test_nullable_types() {
        let int_type = DoletType::Int;
        let nullable_int = int_type.make_nullable();
        assert!(nullable_int.is_nullable());
        assert_eq!(nullable_int.unwrap_nullable(), &DoletType::Int);
    }
}
