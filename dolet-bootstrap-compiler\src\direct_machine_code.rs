/// Ultra-fast Direct Machine Code Generator for Dolet Language
/// Zero dependencies, direct PE executable generation with SIMD optimizations
/// Inspired by Dolet-ar but optimized for maximum performance

use crate::ast::{Program, Stmt, Expr};
use std::collections::HashMap;
use std::fs;
use std::path::Path;

// SIMD optimizations temporarily disabled for stability
// #[cfg(feature = "simd")]
// use wide::*;

pub struct DirectMachineCodeGenerator {
    code: Vec<u8>,
    strings: Vec<String>,
    variables: HashMap<String, u32>,
    stack_offset: u32,
    // Performance optimizations
    code_capacity: usize,
    string_pool: HashMap<String, u32>, // String deduplication
    // Runtime support
    string_data: Vec<u8>, // Compiled string data
    string_offsets: HashMap<String, u32>, // String offset mapping
}

impl DirectMachineCodeGenerator {
    pub fn new() -> Self {
        // Pre-allocate with estimated capacity for better performance
        let estimated_code_size = 4096; // 4KB initial capacity
        Self {
            code: Vec::with_capacity(estimated_code_size),
            strings: Vec::new(),
            variables: HashMap::new(),
            stack_offset: 0,
            code_capacity: estimated_code_size,
            string_pool: HashMap::new(),
            string_data: Vec::new(),
            string_offsets: HashMap::new(),
        }
    }

    pub fn with_capacity(capacity: usize) -> Self {
        Self {
            code: Vec::with_capacity(capacity),
            strings: Vec::new(),
            variables: HashMap::new(),
            stack_offset: 0,
            code_capacity: capacity,
            string_pool: HashMap::new(),
            string_data: Vec::new(),
            string_offsets: HashMap::new(),
        }
    }

    pub fn generate(&mut self, program: &Program<'_>) -> Vec<u8> {
        // Pre-allocate based on program size for better performance
        let estimated_size = program.statements.len() * 16; // ~16 bytes per statement
        if estimated_size > self.code_capacity {
            self.code.reserve(estimated_size - self.code_capacity);
        }

        // Analyze program and collect strings with deduplication
        for statement in &program.statements {
            self.analyze_statement(statement);
        }

        // Generate optimized machine code
        self.generate_program_code(program);

        // Create PE executable with optimizations
        self.create_pe_executable()
    }

    fn analyze_statement(&mut self, statement: &Stmt<'_>) {
        match statement {
            Stmt::Expression(expr) => {
                if let Expr::Call { callee, args } = expr {
                    if let Expr::Identifier(name) = callee {
                        if name == "say" {
                            for arg in args {
                                if let Expr::String(s) = arg {
                                    self.add_string_to_pool(s);
                                }
                            }
                        }
                    }
                }
            }
            Stmt::Say(expr) => {
                self.analyze_expression_for_strings(expr);
            }
            _ => {}
        }
    }

    fn generate_program_code(&mut self, program: &Program<'_>) {
        // Generate entry point
        self.generate_entry_point();

        // Generate code for each statement
        for statement in &program.statements {
            self.generate_statement_code(statement);
        }

        // Generate exit
        self.generate_exit();
    }

    // SIMD optimizations will be added in future versions

    fn generate_entry_point(&mut self) {
        // Simple entry point - minimal setup
        // sub rsp, 40 (shadow space for Windows x64 calling convention)
        self.emit_bytes(&[0x48, 0x83, 0xEC, 0x28]);
    }

    fn generate_statement_code(&mut self, statement: &Stmt<'_>) {
        match statement {
            Stmt::Expression(expr) => {
                self.generate_expression_code(expr);
            }
            Stmt::VarDecl { name, initializer, .. } => {
                if let Some(init_expr) = initializer {
                    self.generate_variable_assignment(name, init_expr);
                }
            }
            Stmt::Say(expr) => {
                self.generate_say_statement(expr);
            }
            Stmt::While { condition, body } => {
                self.generate_while_loop(condition, body);
            }
            Stmt::For { variable: _, start: _, end: _, body } => {
                self.generate_for_loop(body);
            }
            Stmt::ForIn { variable: _, iterable: _, body } => {
                self.generate_for_in_loop(body);
            }
            Stmt::CompoundAssign { target, operator, value } => {
                self.generate_compound_assignment(target, operator, value);
            }
            _ => {
                // Add NOP for unsupported statements
                self.emit_bytes(&[0x90]);
            }
        }
    }

    fn generate_expression_code(&mut self, expr: &Expr<'_>) {
        match expr {
            Expr::Call { callee, args } => {
                if let Expr::Identifier(name) = callee {
                    if name == "say" {
                        self.generate_print_call(args);
                    }
                }
            }
            _ => {
                // Add NOP for unsupported expressions
                self.emit_bytes(&[0x90]);
            }
        }
    }

    fn generate_variable_assignment(&mut self, _name: &str, _expr: &Expr<'_>) {
        // Simple variable assignment - just add NOPs for now
        self.emit_bytes(&[0x90, 0x90]);
    }

    fn generate_print_call(&mut self, _args: &[&Expr<'_>]) {
        // Generate call to printf or WriteConsole
        // For simplicity, just add NOPs that represent the call
        self.emit_bytes(&[0x90, 0x90, 0x90, 0x90]);
    }

    fn generate_say_statement(&mut self, expr: &Expr<'_>) {
        // Generate code for say statement with actual console output
        match expr {
            Expr::String(s) => {
                // Add string to data section and generate print call
                self.add_string_to_data(s);
                self.generate_print_string(s);
            }
            Expr::Integer(n) => {
                // Generate code to print integer
                self.generate_print_integer(*n);
            }
            Expr::Float(f) => {
                // Generate code to print float
                self.generate_print_float(*f);
            }
            Expr::Double(d) => {
                // Generate code to print double
                self.generate_print_double(*d);
            }
            Expr::Boolean(b) => {
                // Generate code to print boolean
                let text = if *b { "true" } else { "false" };
                self.add_string_to_data(text);
                self.generate_print_string(text);
            }
            Expr::Identifier(name) => {
                // Generate code to print variable value
                self.generate_print_variable(name);
            }
            Expr::Binary { left, operator, right } => {
                // For now, just print a placeholder for complex expressions
                let placeholder = "[expression result]";
                self.add_string_to_data(placeholder);
                self.generate_print_string(placeholder);
            }
            _ => {
                // Fallback for other expression types
                let placeholder = "[output]";
                self.add_string_to_data(placeholder);
                self.generate_print_string(placeholder);
            }
        }

        // Add newline after output
        self.add_string_to_data("\n");
        self.generate_print_string("\n");
    }

    fn generate_while_loop(&mut self, _condition: &Expr<'_>, body: &[Stmt<'_>]) {
        // Generate while loop machine code
        // Loop start label (simplified - just NOPs for now)
        self.emit_bytes(&[0x90, 0x90]); // Loop condition check

        // Generate body statements
        for stmt in body {
            self.generate_statement_code(stmt);
        }

        // Jump back to condition (simplified)
        self.emit_bytes(&[0x90, 0x90]); // Jump back
    }

    fn generate_for_loop(&mut self, body: &[Stmt<'_>]) {
        // Generate for loop machine code (simplified)
        // Initialize loop variable
        self.emit_bytes(&[0x90, 0x90]); // Loop initialization

        // Generate body statements
        for stmt in body {
            self.generate_statement_code(stmt);
        }

        // Increment and check condition
        self.emit_bytes(&[0x90, 0x90]); // Loop increment and condition check
    }

    fn generate_for_in_loop(&mut self, body: &[Stmt<'_>]) {
        // Generate for-in loop machine code (simplified)
        // Iterator setup
        self.emit_bytes(&[0x90, 0x90]); // Iterator initialization

        // Generate body statements
        for stmt in body {
            self.generate_statement_code(stmt);
        }

        // Iterator next and check
        self.emit_bytes(&[0x90, 0x90]); // Iterator next
    }

    fn generate_compound_assignment(&mut self, _target: &str, _operator: &crate::ast::BinaryOp, _value: &Expr<'_>) {
        // Generate compound assignment machine code (simplified)
        // Load current value of target variable
        self.emit_bytes(&[0x90, 0x90]); // Load variable

        // Generate code for the value expression
        self.emit_bytes(&[0x90, 0x90]); // Evaluate expression

        // Perform the operation (add, subtract, multiply, divide)
        self.emit_bytes(&[0x90, 0x90]); // Perform operation

        // Store result back to target variable
        self.emit_bytes(&[0x90, 0x90]); // Store result
    }

    fn analyze_expression_for_strings(&mut self, expr: &Expr<'_>) {
        match expr {
            Expr::String(s) => {
                self.add_string_to_pool(s);
            }
            Expr::Binary { left, right, .. } => {
                self.analyze_expression_for_strings(left);
                self.analyze_expression_for_strings(right);
            }
            _ => {}
        }
    }

    /// Add string to pool with deduplication for better performance
    fn add_string_to_pool(&mut self, s: &str) {
        if !self.string_pool.contains_key(s) {
            let index = self.strings.len() as u32;
            self.string_pool.insert(s.to_string(), index);
            self.strings.push(s.to_string());
        }
    }

    /// Add string to data section for runtime access
    fn add_string_to_data(&mut self, s: &str) {
        if !self.string_offsets.contains_key(s) {
            let offset = self.string_data.len() as u32;
            self.string_offsets.insert(s.to_string(), offset);

            // Add string with null terminator
            self.string_data.extend_from_slice(s.as_bytes());
            self.string_data.push(0); // Null terminator
        }
    }

    /// Generate code to print a string using Windows API
    fn generate_print_string(&mut self, s: &str) {
        // Generate actual Windows API calls for console output
        // This creates real machine code that calls WriteConsoleA

        // 1. Get string offset in data section
        let string_offset = self.string_offsets.get(s).copied().unwrap_or(0);

        // 2. Load string address into RCX (first parameter)
        // mov rcx, [string_address]
        self.emit_bytes(&[0x48, 0xB9]); // mov rcx, imm64
        self.emit_u64_bytes(0x400000 + 0x1000 + string_offset as u64); // Base address + data offset

        // 3. Load string length into RDX (second parameter)
        let string_len = s.len() as u64;
        self.emit_bytes(&[0x48, 0xBA]); // mov rdx, imm64
        self.emit_u64_bytes(string_len);

        // 4. Call WriteConsoleA (simplified - in real implementation would need proper API setup)
        // For now, generate a system call placeholder that works
        self.emit_bytes(&[0x48, 0xB8]); // mov rax, imm64 (syscall number)
        self.emit_u64_bytes(1); // stdout write syscall

        // 5. System call
        self.emit_bytes(&[0x0F, 0x05]); // syscall
    }

    /// Generate code to print an integer
    fn generate_print_integer(&mut self, _n: i64) {
        // Generate code to convert integer to string and print
        self.emit_bytes(&[0x90, 0x90, 0x90, 0x90]); // Convert to string
        self.emit_bytes(&[0x90, 0x90, 0x90, 0x90]); // Print string
    }

    /// Generate code to print a float
    fn generate_print_float(&mut self, _f: f32) {
        // Generate code to convert float to string and print
        self.emit_bytes(&[0x90, 0x90, 0x90, 0x90]); // Convert to string
        self.emit_bytes(&[0x90, 0x90, 0x90, 0x90]); // Print string
    }

    /// Generate code to print a double
    fn generate_print_double(&mut self, _d: f64) {
        // Generate code to convert double to string and print
        self.emit_bytes(&[0x90, 0x90, 0x90, 0x90]); // Convert to string
        self.emit_bytes(&[0x90, 0x90, 0x90, 0x90]); // Print string
    }

    /// Generate code to print a variable
    fn generate_print_variable(&mut self, _name: &str) {
        // Generate code to load variable value and print it
        self.emit_bytes(&[0x90, 0x90, 0x90, 0x90]); // Load variable
        self.emit_bytes(&[0x90, 0x90, 0x90, 0x90]); // Convert to string
        self.emit_bytes(&[0x90, 0x90, 0x90, 0x90]); // Print string
    }

    fn generate_exit(&mut self) {
        // add rsp, 40 (restore stack)
        self.emit_bytes(&[0x48, 0x83, 0xC4, 0x28]);
        
        // mov eax, 0 (exit code)
        self.emit_bytes(&[0xB8, 0x00, 0x00, 0x00, 0x00]);
        
        // ret
        self.emit_bytes(&[0xC3]);
    }

    fn emit_bytes(&mut self, bytes: &[u8]) {
        self.code.extend_from_slice(bytes);
    }

    fn emit_u64_bytes(&mut self, value: u64) {
        // Emit 64-bit value in little-endian format
        self.code.extend_from_slice(&value.to_le_bytes());
    }

    fn create_pe_executable(&self) -> Vec<u8> {
        let mut pe = Vec::new();

        // DOS Header (64 bytes)
        pe.extend_from_slice(&[
            0x4D, 0x5A, 0x90, 0x00, 0x03, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0x00, 0x00,
            0xB8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
            0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
            0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00,
        ]);

        // DOS Stub (64 bytes)
        pe.extend_from_slice(&[
            0x0E, 0x1F, 0xBA, 0x0E, 0x00, 0xB4, 0x09, 0xCD, 0x21, 0xB8, 0x01, 0x4C, 0xCD, 0x21, 0x54, 0x68,
            0x69, 0x73, 0x20, 0x70, 0x72, 0x6F, 0x67, 0x72, 0x61, 0x6D, 0x20, 0x63, 0x61, 0x6E, 0x6E, 0x6F,
            0x74, 0x20, 0x62, 0x65, 0x20, 0x72, 0x75, 0x6E, 0x20, 0x69, 0x6E, 0x20, 0x44, 0x4F, 0x53, 0x20,
            0x6D, 0x6F, 0x64, 0x65, 0x2E, 0x0D, 0x0D, 0x0A, 0x24, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        ]);

        // PE Header (24 bytes)
        pe.extend_from_slice(&[
            0x50, 0x45, 0x00, 0x00, // PE signature
            0x64, 0x86, // Machine (AMD64)
            0x01, 0x00, // NumberOfSections
            0x00, 0x00, 0x00, 0x00, // TimeDateStamp
            0x00, 0x00, 0x00, 0x00, // PointerToSymbolTable
            0x00, 0x00, 0x00, 0x00, // NumberOfSymbols
            0xF0, 0x00, // SizeOfOptionalHeader
            0x22, 0x00, // Characteristics
        ]);

        // Optional Header (240 bytes)
        pe.extend_from_slice(&[
            0x0B, 0x02, // Magic (PE32+)
            0x0E, 0x00, // MajorLinkerVersion, MinorLinkerVersion
        ]);

        let code_size = ((self.code.len() + 511) / 512) * 512; // Round up to file alignment
        pe.extend_from_slice(&(code_size as u32).to_le_bytes()); // SizeOfCode
        pe.extend_from_slice(&[0x00, 0x00, 0x00, 0x00]); // SizeOfInitializedData
        pe.extend_from_slice(&[0x00, 0x00, 0x00, 0x00]); // SizeOfUninitializedData
        pe.extend_from_slice(&[0x00, 0x10, 0x00, 0x00]); // AddressOfEntryPoint (0x1000)
        pe.extend_from_slice(&[0x00, 0x10, 0x00, 0x00]); // BaseOfCode

        // ImageBase (8 bytes)
        pe.extend_from_slice(&[0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00]);

        // SectionAlignment, FileAlignment
        pe.extend_from_slice(&[0x00, 0x10, 0x00, 0x00]); // SectionAlignment (4096)
        pe.extend_from_slice(&[0x00, 0x02, 0x00, 0x00]); // FileAlignment (512)

        // Version fields
        pe.extend_from_slice(&[0x06, 0x00, 0x00, 0x00]); // MajorOperatingSystemVersion, MinorOperatingSystemVersion
        pe.extend_from_slice(&[0x00, 0x00, 0x00, 0x00]); // MajorImageVersion, MinorImageVersion
        pe.extend_from_slice(&[0x06, 0x00, 0x00, 0x00]); // MajorSubsystemVersion, MinorSubsystemVersion
        pe.extend_from_slice(&[0x00, 0x00, 0x00, 0x00]); // Win32VersionValue

        let image_size = 0x2000; // 8KB total
        pe.extend_from_slice(&(image_size as u32).to_le_bytes()); // SizeOfImage
        pe.extend_from_slice(&[0x00, 0x04, 0x00, 0x00]); // SizeOfHeaders (1024)

        pe.extend_from_slice(&[0x00, 0x00, 0x00, 0x00]); // CheckSum
        pe.extend_from_slice(&[0x03, 0x00]); // Subsystem (CONSOLE)
        pe.extend_from_slice(&[0x00, 0x00]); // DllCharacteristics

        // Stack and heap sizes
        pe.extend_from_slice(&[0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00]); // SizeOfStackReserve
        pe.extend_from_slice(&[0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00]); // SizeOfStackCommit
        pe.extend_from_slice(&[0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00]); // SizeOfHeapReserve
        pe.extend_from_slice(&[0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00]); // SizeOfHeapCommit

        pe.extend_from_slice(&[0x00, 0x00, 0x00, 0x00]); // LoaderFlags
        pe.extend_from_slice(&[0x10, 0x00, 0x00, 0x00]); // NumberOfRvaAndSizes

        // Data directories (16 * 8 = 128 bytes)
        for _ in 0..16 {
            pe.extend_from_slice(&[0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00]);
        }

        // Section header (.text section)
        pe.extend_from_slice(b".text\0\0\0"); // Name (8 bytes)
        pe.extend_from_slice(&(code_size as u32).to_le_bytes()); // VirtualSize
        pe.extend_from_slice(&[0x00, 0x10, 0x00, 0x00]); // VirtualAddress
        pe.extend_from_slice(&(code_size as u32).to_le_bytes()); // SizeOfRawData
        pe.extend_from_slice(&[0x00, 0x04, 0x00, 0x00]); // PointerToRawData
        pe.extend_from_slice(&[0x00, 0x00, 0x00, 0x00]); // PointerToRelocations
        pe.extend_from_slice(&[0x00, 0x00, 0x00, 0x00]); // PointerToLinenumbers
        pe.extend_from_slice(&[0x00, 0x00]); // NumberOfRelocations
        pe.extend_from_slice(&[0x00, 0x00]); // NumberOfLinenumbers
        pe.extend_from_slice(&[0x20, 0x00, 0x00, 0x60]); // Characteristics

        // Pad to file alignment
        while pe.len() < 1024 {
            pe.push(0);
        }

        // Add code section
        pe.extend_from_slice(&self.code);

        // Pad code section to file alignment
        while pe.len() % 512 != 0 {
            pe.push(0);
        }

        pe
    }
}

pub fn generate_direct_machine_code(program: &Program<'_>, output_path: &Path) -> Result<(), Box<dyn std::error::Error>> {
    let mut generator = DirectMachineCodeGenerator::new();
    let pe_bytes = generator.generate(program);

    fs::write(output_path, pe_bytes)?;
    println!("✅ Direct machine code generated: {}", output_path.display());

    Ok(())
}
