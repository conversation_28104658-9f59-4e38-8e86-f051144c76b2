{"rustc": 10895048813736897673, "features": "[\"default\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 3592778941406178886, "path": 3674464971012850586, "deps": [[555019317135488525, "regex_automata", false, 3754837323018564271], [2779309023524819297, "aho_corasick", false, 11819528853513002830], [9408802513701742484, "regex_syntax", false, 18353092114167597600], [15932120279885307830, "memchr", false, 4885371166786211403]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\regex-8baa14686da77754\\dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}