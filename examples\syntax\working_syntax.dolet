# Dolet Language - ACTUALLY WORKING Features Only
# This file contains ONLY features that are currently implemented and tested

# ============================================================================
# COMMENTS
# ============================================================================
# Single line comments start with #

# ============================================================================
# VARIABLE DECLARATIONS
# ============================================================================

# Basic variable declaration
set name = "Alice"
set age = 25
set height = 5.8
set is_student = true

# Variables with type annotations
set count: int = 0
set price: float = 19.99
set message: string = "Hello World"
set active: bool = false

# Constants (immutable)
const PI = 3.14159
const MAX_SIZE = 100

# ============================================================================
# DATA TYPES (WORKING)
# ============================================================================

# Integers
set small_num = 42
set large_num = 1000000
set negative = -50

# Floating point numbers
set simple_float = 3.14
set precise = 2.718281828

# Strings
set greeting = "Hello, World!"
set empty_string = ""

# Characters
set letter = 'A'
set digit = '5'

# Booleans
set is_true = true
set is_false = false

# Arrays (basic creation only)
set numbers = [1, 2, 3, 4, 5]
set names = ["Alice", "Bob", "Charlie"]

# ============================================================================
# OPERATORS (WORKING)
# ============================================================================

# Arithmetic operators
set addition = 5 + 3        # 8
set subtraction = 10 - 4    # 6
set multiplication = 6 * 7  # 42
set division = 15 / 3       # 5
set modulo = 17 % 5         # 2

# Comparison operators
set equal = (5 == 5)        # true
set not_equal = (5 != 3)    # true
set less_than = (3 < 5)     # true
set greater_than = (7 > 4)  # true
set less_equal = (5 <= 5)   # true
set greater_equal = (8 >= 6) # true

# Logical operators
set and_result = true && false  # false
set or_result = true || false   # true
set not_result = !true          # false

# String concatenation (WORKS with mixed types!)
set full_name = "John" + " " + "Doe"
set message_with_number = "Count: " + 42
set complex_concat = "Value " + 123 + " is " + true

# Compound assignment operators
set counter = 10
set counter += 5    # counter = 15
set counter -= 3    # counter = 12
set counter *= 2    # counter = 24
set counter /= 4    # counter = 6

# ============================================================================
# CONTROL FLOW (WORKING)
# ============================================================================

# If statements
if age >= 18:
    say "You are an adult"
end

# If-else statements
if temperature > 30:
    say "It's hot outside"
else:
    say "It's not too hot"
end

# Nested if-else (since else-if is not supported)
if score >= 90:
    say "Grade: A"
else:
    if score >= 80:
        say "Grade: B"
    else:
        if score >= 70:
            say "Grade: C"
        else:
            say "Grade: F"
        end
    end
end

# While loops - BOTH syntaxes work!
set i = 0
while i < 5:
    say "Count: " + i
    set i = i + 1
end

# Alternative while syntax
set j = 0
while j < 3 do
    say "Alternative syntax: " + j
    set j = j + 1
end

# Variable declarations inside loops (RECENTLY FIXED!)
set loop_count = 0
while loop_count < 3 do
    set loop_variable = loop_count * 10  # This works now!
    say "Loop " + loop_count + ": variable = " + loop_variable
    set loop_count = loop_count + 1
end

# ============================================================================
# FUNCTIONS (WORKING)
# ============================================================================

# Function declaration
fun greet(name):
    say "Hello, " + name + "!"
end

# Function with return value
fun add(a, b):
    return a + b
end

# Function with type annotations
fun multiply(x: int, y: int): int
    return x * y
end

# Function calls
greet("Alice")
set result = add(10, 20)
set product = multiply(5, 6)

# ============================================================================
# INPUT/OUTPUT (WORKING)
# ============================================================================

# Output to console - ONLY say works
say "Hello, World!"
say "The answer is: " + 42
say "Complex: " + "Value " + 123 + " works!"

# ============================================================================
# WORKING EXAMPLES
# ============================================================================

# Example 1: Simple calculator
fun calculator_demo():
    set a = 10
    set b = 5
    say "Addition: " + (a + b)
    say "Subtraction: " + (a - b)
    say "Multiplication: " + (a * b)
    say "Division: " + (a / b)
end

# Example 2: Factorial function
fun factorial(n):
    if n <= 1:
        return 1
    else:
        return n * factorial(n - 1)
    end
end

# Example 3: Counter with loop variables
fun count_demo():
    set counter = 0
    while counter < 5 do
        set doubled = counter * 2
        set message = "Counter: " + counter + ", Doubled: " + doubled
        say message
        set counter = counter + 1
    end
end

# ============================================================================
# WHAT DOES NOT WORK (Don't use these!)
# ============================================================================

# ❌ For loops - NOT IMPLEMENTED
# for i from 1 to 5:     # DON'T USE
# for item in array:     # DON'T USE

# ❌ Array indexing - NOT WORKING PROPERLY
# set item = array[0]    # DON'T USE

# ❌ Built-in functions - NOT IMPLEMENTED
# ask "prompt"           # DON'T USE
# length(array)          # DON'T USE
# sqrt(16)               # DON'T USE

# ❌ File operations - NOT IMPLEMENTED
# read "file.txt"        # DON'T USE
# write "file.txt", data # DON'T USE

# ❌ Advanced features - NOT IMPLEMENTED
# Multiple assignments   # DON'T USE
# Default parameters     # DON'T USE
# Ternary operator      # DON'T USE

# ============================================================================
# COMPILATION COMMANDS (WORKING)
# ============================================================================

# Basic compilation:
# cargo run --release --bin dolet -- your_file.dolet

# With timing information:
# cargo run --release --bin dolet -- your_file.dolet --time

# ============================================================================
# RECENTLY FIXED FEATURES (All working perfectly!)
# ============================================================================

# ✅ Variable declarations inside loops
# ✅ Complex string concatenation with mixed types
# ✅ Both while loop syntaxes (: and do)
# ✅ Proper assignment vs declaration in loops
# ✅ Nested function calls
# ✅ Type-safe string operations

# ============================================================================
# USAGE NOTES
# ============================================================================

# 1. Only use features listed as "WORKING" above
# 2. Avoid features in the "WHAT DOES NOT WORK" section
# 3. String concatenation with + works with any types
# 4. Both while syntaxes work: "while condition:" and "while condition do"
# 5. Variables declared inside loops are properly scoped
# 6. Functions can call other functions
# 7. Comments start with # and go to end of line
# 8. Use 'end' keyword to close all blocks
